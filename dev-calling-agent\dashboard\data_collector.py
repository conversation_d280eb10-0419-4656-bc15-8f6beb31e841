"""
Data Collection Service for Agent Dashboard
Captures and stores agent interactions, transcriptions, and tool calls in real-time
"""
import asyncio
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from .database import get_db, AgentSession, Transcription, ToolCall, SessionLog
import threading
from queue import Queue
import logging

logger = logging.getLogger(__name__)

class DataCollector:
    """Singleton data collector for agent interactions"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(DataCollector, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.current_session_id = None
            self.data_queue = Queue()
            self.is_running = False
            self.worker_thread = None
            self.initialized = True
            logger.info("DataCollector initialized")
    
    def start_session(self, user_id: Optional[str] = None, session_type: str = "voice", metadata: Optional[Dict] = None) -> str:
        """Start a new agent session"""
        session_id = str(uuid.uuid4())
        self.current_session_id = session_id
        
        session_data = {
            'type': 'session_start',
            'session_id': session_id,
            'user_id': user_id,
            'session_type': session_type,
            'metadata': metadata or {},
            'timestamp': datetime.utcnow()
        }
        
        self._queue_data(session_data)
        logger.info(f"Started session: {session_id}")
        return session_id
    
    def end_session(self, session_id: Optional[str] = None):
        """End the current or specified session"""
        target_session = session_id or self.current_session_id
        if target_session:
            session_data = {
                'type': 'session_end',
                'session_id': target_session,
                'timestamp': datetime.utcnow()
            }
            self._queue_data(session_data)
            
            if target_session == self.current_session_id:
                self.current_session_id = None
            
            logger.info(f"Ended session: {target_session}")
    
    def log_transcription(self, 
                         original_text: str,
                         language_detected: Optional[str] = None,
                         confidence_score: Optional[float] = None,
                         audio_duration: Optional[float] = None,
                         transliterated_text: Optional[str] = None,
                         target_language: Optional[str] = None,
                         agent_response: Optional[str] = None,
                         response_language: Optional[str] = None,
                         response_time: Optional[float] = None,
                         processing_time: Optional[float] = None,
                         metadata: Optional[Dict] = None):
        """Log transcription data"""
        if not self.current_session_id:
            logger.warning("No active session for transcription logging")
            return None
        
        transcription_id = str(uuid.uuid4())
        transcription_data = {
            'type': 'transcription',
            'id': transcription_id,
            'session_id': self.current_session_id,
            'original_text': original_text,
            'language_detected': language_detected,
            'confidence_score': confidence_score,
            'audio_duration': audio_duration,
            'transliterated_text': transliterated_text,
            'target_language': target_language,
            'agent_response': agent_response,
            'response_language': response_language,
            'response_time': response_time,
            'processing_time': processing_time,
            'metadata': metadata or {},
            'timestamp': datetime.utcnow()
        }
        
        self._queue_data(transcription_data)
        logger.debug(f"Logged transcription: {transcription_id}")
        return transcription_id
    
    def log_tool_call(self,
                     tool_name: str,
                     input_query: str,
                     tool_description: Optional[str] = None,
                     execution_time: Optional[float] = None,
                     status: str = "pending",
                     output_result: Optional[str] = None,
                     result_count: Optional[int] = None,
                     is_relevant: Optional[bool] = None,
                     error_message: Optional[str] = None,
                     error_type: Optional[str] = None,
                     transcription_id: Optional[str] = None,
                     metadata: Optional[Dict] = None):
        """Log tool call data"""
        if not self.current_session_id:
            logger.warning("No active session for tool call logging")
            return None
        
        tool_call_id = str(uuid.uuid4())
        tool_call_data = {
            'type': 'tool_call',
            'id': tool_call_id,
            'session_id': self.current_session_id,
            'transcription_id': transcription_id,
            'tool_name': tool_name,
            'tool_description': tool_description,
            'input_query': input_query,
            'execution_time': execution_time,
            'status': status,
            'output_result': output_result,
            'result_count': result_count,
            'is_relevant': is_relevant,
            'error_message': error_message,
            'error_type': error_type,
            'metadata': metadata or {},
            'timestamp': datetime.utcnow()
        }
        
        self._queue_data(tool_call_data)
        logger.debug(f"Logged tool call: {tool_call_id}")
        return tool_call_id
    
    def log_session_event(self,
                         level: str,
                         message: str,
                         component: Optional[str] = None,
                         execution_time: Optional[float] = None,
                         memory_usage: Optional[float] = None,
                         metadata: Optional[Dict] = None):
        """Log session events and performance metrics"""
        if not self.current_session_id:
            logger.warning("No active session for event logging")
            return None
        
        log_id = str(uuid.uuid4())
        log_data = {
            'type': 'session_log',
            'id': log_id,
            'session_id': self.current_session_id,
            'level': level,
            'message': message,
            'component': component,
            'execution_time': execution_time,
            'memory_usage': memory_usage,
            'metadata': metadata or {},
            'timestamp': datetime.utcnow()
        }
        
        self._queue_data(log_data)
        logger.debug(f"Logged session event: {log_id}")
        return log_id
    
    def update_session_language(self, language_code: str, language_config: Optional[Dict] = None):
        """Update session language information"""
        if not self.current_session_id:
            return
        
        update_data = {
            'type': 'session_update',
            'session_id': self.current_session_id,
            'language_detected': language_code,
            'language_config': language_config,
            'timestamp': datetime.utcnow()
        }
        
        self._queue_data(update_data)
        logger.debug(f"Updated session language: {language_code}")
    
    def _queue_data(self, data: Dict[str, Any]):
        """Queue data for processing"""
        self.data_queue.put(data)
        
        # Start worker thread if not running
        if not self.is_running:
            self.start_worker()
    
    def start_worker(self):
        """Start the background worker thread"""
        if not self.is_running:
            self.is_running = True
            self.worker_thread = threading.Thread(target=self._process_queue, daemon=True)
            self.worker_thread.start()
            logger.info("Data collector worker started")
    
    def stop_worker(self):
        """Stop the background worker thread"""
        self.is_running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        logger.info("Data collector worker stopped")
    
    def _process_queue(self):
        """Process queued data in background thread"""
        while self.is_running:
            try:
                if not self.data_queue.empty():
                    data = self.data_queue.get(timeout=1)
                    self._save_to_database(data)
                else:
                    time.sleep(0.1)  # Small delay when queue is empty
            except Exception as e:
                logger.error(f"Error processing queue: {e}")
                time.sleep(1)  # Wait before retrying
    
    def _save_to_database(self, data: Dict[str, Any]):
        """Save data to database"""
        try:
            db = next(get_db())
            
            if data['type'] == 'session_start':
                session = AgentSession(
                    id=data['session_id'],
                    user_id=data.get('user_id'),
                    session_type=data.get('session_type', 'voice'),
                    metadata=data.get('metadata'),
                    created_at=data['timestamp']
                )
                db.add(session)
                
            elif data['type'] == 'session_end':
                session = db.query(AgentSession).filter(AgentSession.id == data['session_id']).first()
                if session:
                    session.ended_at = data['timestamp']
                    session.status = 'ended'
                    
            elif data['type'] == 'session_update':
                session = db.query(AgentSession).filter(AgentSession.id == data['session_id']).first()
                if session:
                    session.language_detected = data.get('language_detected')
                    session.language_config = data.get('language_config')
                    
            elif data['type'] == 'transcription':
                transcription = Transcription(
                    id=data['id'],
                    session_id=data['session_id'],
                    original_text=data['original_text'],
                    language_detected=data.get('language_detected'),
                    confidence_score=data.get('confidence_score'),
                    audio_duration=data.get('audio_duration'),
                    transliterated_text=data.get('transliterated_text'),
                    target_language=data.get('target_language'),
                    agent_response=data.get('agent_response'),
                    response_language=data.get('response_language'),
                    response_time=data.get('response_time'),
                    processing_time=data.get('processing_time'),
                    metadata=data.get('metadata'),
                    timestamp=data['timestamp']
                )
                db.add(transcription)
                
            elif data['type'] == 'tool_call':
                tool_call = ToolCall(
                    id=data['id'],
                    session_id=data['session_id'],
                    transcription_id=data.get('transcription_id'),
                    tool_name=data['tool_name'],
                    tool_description=data.get('tool_description'),
                    input_query=data['input_query'],
                    execution_time=data.get('execution_time'),
                    status=data.get('status', 'pending'),
                    output_result=data.get('output_result'),
                    result_count=data.get('result_count'),
                    is_relevant=data.get('is_relevant'),
                    error_message=data.get('error_message'),
                    error_type=data.get('error_type'),
                    metadata=data.get('metadata'),
                    timestamp=data['timestamp']
                )
                db.add(tool_call)
                
            elif data['type'] == 'session_log':
                session_log = SessionLog(
                    id=data['id'],
                    session_id=data['session_id'],
                    level=data['level'],
                    message=data['message'],
                    component=data.get('component'),
                    execution_time=data.get('execution_time'),
                    memory_usage=data.get('memory_usage'),
                    metadata=data.get('metadata'),
                    timestamp=data['timestamp']
                )
                db.add(session_log)
            
            db.commit()
            
        except Exception as e:
            logger.error(f"Database save error: {e}")
            if db:
                db.rollback()
        finally:
            if db:
                db.close()

# Global instance
data_collector = DataCollector()
