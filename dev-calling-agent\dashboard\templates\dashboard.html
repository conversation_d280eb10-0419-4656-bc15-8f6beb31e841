<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Dashboard - Professional Monitoring</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-robot me-2"></i>
                Agent Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item">
                    <span class="nav-link">
                        <i class="fas fa-circle text-success me-1" id="connection-status"></i>
                        <span id="connection-text">Connected</span>
                    </span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Analytics Overview -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-chart-line me-2"></i>Analytics Overview</h2>
            </div>
        </div>
        
        <div class="row mb-4" id="analytics-cards">
            <!-- Analytics cards will be populated by JavaScript -->
        </div>

        <!-- Real-time Activity -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-activity me-2"></i>
                            Real-time Activity
                        </h5>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" onclick="clearActivity()">
                                <i class="fas fa-trash me-1"></i>Clear
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="toggleAutoScroll()" id="auto-scroll-btn">
                                <i class="fas fa-pause me-1"></i>Pause
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="activity-feed" class="activity-feed">
                            <!-- Real-time activity will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sessions Table -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-comments me-2"></i>
                            Recent Sessions
                        </h5>
                        <div>
                            <select class="form-select form-select-sm" id="session-filter" onchange="loadSessions()">
                                <option value="">All Sessions</option>
                                <option value="active">Active</option>
                                <option value="ended">Ended</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Session ID</th>
                                        <th>Started</th>
                                        <th>Duration</th>
                                        <th>Language</th>
                                        <th>Transcriptions</th>
                                        <th>Tool Calls</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sessions-table-body">
                                    <!-- Sessions will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Session Details Modal -->
    <div class="modal fade" id="sessionModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-info-circle me-2"></i>
                        Session Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="session-details-content">
                        <!-- Session details will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/static/js/dashboard.js"></script>
</body>
</html>
