# Agent Dashboard

A professional real-time dashboard for monitoring agent interactions, transcriptions, transliterations, and tool calls with proper session tracking and analytics.

## Features

### 🎯 Core Features
- **Real-time Monitoring**: Live tracking of agent conversations and tool usage
- **Session Management**: Complete session lifecycle tracking with unique IDs
- **Transcription Tracking**: Audio-to-text conversion monitoring with confidence scores
- **Transliteration Support**: Multi-language text processing and conversion
- **Tool Call Analytics**: Detailed tracking of vector database and web search operations
- **Performance Metrics**: Response times, success rates, and execution analytics

### 📊 Dashboard Components
- **Analytics Overview**: Key metrics and performance indicators
- **Real-time Activity Feed**: Live stream of agent activities
- **Session Browser**: Detailed session history and filtering
- **Conversation Timeline**: Complete interaction history per session
- **Language Analytics**: Multi-language usage statistics

### 🔧 Technical Features
- **FastAPI Backend**: High-performance REST API with automatic documentation
- **SQLite Database**: Local storage with proper schema and relationships
- **WebSocket Support**: Real-time updates without page refresh
- **Professional UI**: Bootstrap-based responsive design
- **Data Export**: Session data export capabilities

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Agent Core    │───▶│  Data Collector │───▶│    Database     │
│  (LangGraph)    │    │   (Background)  │    │   (SQLite)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │◀───│  FastAPI Server │◀───│  WebSocket Hub  │
│  (Dashboard)    │    │   (REST API)    │    │  (Real-time)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Installation

### 1. Install Dependencies
```bash
cd dev-calling-agent
pip install -r requirements.txt
```

### 2. Initialize Database
```bash
python -c "from dashboard.database import init_database; init_database()"
```

## Usage

### Starting the Dashboard Server

```bash
# Basic usage
python dashboard_server.py

# Custom host and port
python dashboard_server.py --host 127.0.0.1 --port 8080

# Development mode with auto-reload
python dashboard_server.py --reload --log-level debug
```

### Starting the Agent with Dashboard Integration

```bash
# The agent will automatically connect to the dashboard
python langgraph-agent.py
```

### Accessing the Dashboard

1. **Main Dashboard**: http://localhost:8000
2. **API Documentation**: http://localhost:8000/docs
3. **WebSocket Endpoint**: ws://localhost:8000/ws

## API Endpoints

### Session Management
- `POST /api/sessions/start` - Start a new session
- `POST /api/sessions/{session_id}/end` - End a session
- `GET /api/sessions` - List sessions with pagination
- `GET /api/sessions/{session_id}` - Get detailed session info

### Data Ingestion
- `POST /api/transcriptions` - Log transcription data
- `POST /api/tool-calls` - Log tool call data
- `POST /api/logs` - Log session events

### Analytics
- `GET /api/analytics/overview` - Get dashboard analytics

## Database Schema

### Tables
1. **agent_sessions**: Session metadata and lifecycle
2. **transcriptions**: Audio transcription and text processing
3. **tool_calls**: Tool execution tracking and results
4. **session_logs**: Detailed session events and performance

### Key Relationships
- Sessions → Transcriptions (1:N)
- Sessions → Tool Calls (1:N)
- Sessions → Logs (1:N)
- Transcriptions → Tool Calls (1:N, optional)

## Dashboard Features

### Analytics Cards
- **Total Sessions**: Active and completed session counts
- **Transcriptions**: Total count with average response time
- **Tool Calls**: Success rate and execution metrics
- **Languages**: Detected languages and usage distribution

### Real-time Activity Feed
- Session start/end events
- New transcriptions with preview
- Tool call executions with status
- Error notifications and alerts

### Session Details Modal
- Complete session information
- Conversation timeline with user/agent interactions
- Tool call details with execution times
- Performance metrics and metadata

## Configuration

### Environment Variables
```bash
# Database
DATABASE_URL=sqlite:///./dashboard.db

# Server
DASHBOARD_HOST=0.0.0.0
DASHBOARD_PORT=8000

# Logging
LOG_LEVEL=info
```

### Customization
- Modify `dashboard/static/css/dashboard.css` for styling
- Update `dashboard/templates/dashboard.html` for layout
- Extend `dashboard/static/js/dashboard.js` for functionality

## Development

### Project Structure
```
dashboard/
├── __init__.py
├── api.py              # FastAPI application
├── database.py         # SQLAlchemy models and database config
├── data_collector.py   # Background data collection service
├── static/
│   ├── css/
│   │   └── dashboard.css
│   └── js/
│       └── dashboard.js
└── templates/
    └── dashboard.html
```

### Adding New Features
1. **Database Changes**: Update models in `database.py`
2. **API Endpoints**: Add routes in `api.py`
3. **Frontend**: Update HTML/CSS/JS in static/templates
4. **Data Collection**: Extend `data_collector.py`

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   ```bash
   # Reinitialize database
   python -c "from dashboard.database import init_database; init_database()"
   ```

2. **WebSocket Connection Failed**
   - Check if dashboard server is running
   - Verify port is not blocked by firewall
   - Check browser console for errors

3. **No Data Appearing**
   - Ensure agent is running with dashboard integration
   - Check data collector is started
   - Verify database permissions

### Logs and Debugging
- Dashboard logs: Check console output
- Agent logs: Check `logs/` directory
- Database: Use SQLite browser to inspect data
- WebSocket: Use browser developer tools

## Performance

### Optimization Tips
- Use pagination for large datasets
- Implement data retention policies
- Monitor database size and performance
- Use WebSocket for real-time updates only

### Scaling Considerations
- Replace SQLite with PostgreSQL for production
- Add Redis for caching and session storage
- Implement horizontal scaling with load balancers
- Add monitoring and alerting systems

## Security

### Best Practices
- Use HTTPS in production
- Implement authentication and authorization
- Sanitize user inputs
- Regular security updates
- Database access controls

## License

This dashboard is part of the Agent project and follows the same licensing terms.
