[ 2025-07-28 13:04:42,330 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 13:04:42,760 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4294 seconds.
[ 2025-07-28 13:04:46,702 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 13:04:54,734 ] 101 root - INFO - Fast RAG chain ready
[ 2025-07-28 13:04:54,734 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-07-28 13:04:54,735 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 13:04:55,478 ] 101 root - INFO - Fast RAG chain ready
[ 2025-07-28 13:04:55,479 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-07-28 13:04:55,480 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 13:04:55,480 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 13:04:55,484 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 13:04:55,486 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 13:04:55,496 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 13:04:55,498 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 13:04:55,498 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 13:04:55,964 ] 397 root - ERROR - Voice agent error: LLM.__init__() got an unexpected keyword argument 'max_tokens'
[ 2025-07-28 13:04:55,965 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 370, in entrypoint
    llm=groq.LLM(
        ^^^^^^^^^
TypeError: LLM.__init__() got an unexpected keyword argument 'max_tokens'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 398, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\langgraph-agent.py] line number [370] error message [LLM.__init__() got an unexpected keyword argument 'max_tokens']
[ 2025-07-28 13:05:31,894 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 13:05:31,895 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 13:05:31,897 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-07-28 13:05:31,898 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 13:05:31,899 ] 278 livekit.agents - DEBUG - job exiting
