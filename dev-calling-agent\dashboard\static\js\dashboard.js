/**
 * Agent Dashboard JavaScript
 * Professional real-time dashboard for monitoring agent interactions
 */

class AgentDashboard {
    constructor() {
        this.websocket = null;
        this.autoScroll = true;
        this.activityCount = 0;
        this.maxActivityItems = 100;
        
        this.init();
    }
    
    init() {
        this.connectWebSocket();
        this.loadAnalytics();
        this.loadSessions();
        
        // Set up periodic refresh
        setInterval(() => {
            this.loadAnalytics();
            this.loadSessions();
        }, 30000); // Refresh every 30 seconds
    }
    
    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        try {
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('WebSocket connected');
                this.updateConnectionStatus(true);
            };
            
            this.websocket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (e) {
                    console.log('WebSocket message:', event.data);
                }
            };
            
            this.websocket.onclose = () => {
                console.log('WebSocket disconnected');
                this.updateConnectionStatus(false);
                
                // Attempt to reconnect after 5 seconds
                setTimeout(() => {
                    this.connectWebSocket();
                }, 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateConnectionStatus(false);
            };
            
        } catch (error) {
            console.error('Failed to connect WebSocket:', error);
            this.updateConnectionStatus(false);
        }
    }
    
    updateConnectionStatus(connected) {
        const statusIcon = document.getElementById('connection-status');
        const statusText = document.getElementById('connection-text');
        
        if (connected) {
            statusIcon.className = 'fas fa-circle text-success me-1';
            statusText.textContent = 'Connected';
            document.body.classList.remove('connection-disconnected');
        } else {
            statusIcon.className = 'fas fa-circle text-danger me-1';
            statusText.textContent = 'Disconnected';
            document.body.classList.add('connection-disconnected');
        }
    }
    
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'session_started':
                this.addActivityItem('session-started', 'Session Started', 
                    `New session: ${data.session_id.substring(0, 8)}...`, data.timestamp);
                this.loadSessions();
                break;
                
            case 'session_ended':
                this.addActivityItem('session-ended', 'Session Ended', 
                    `Session ended: ${data.session_id.substring(0, 8)}...`, data.timestamp);
                this.loadSessions();
                break;
                
            case 'transcription_created':
                this.addActivityItem('transcription', 'New Transcription', 
                    `"${data.original_text.substring(0, 50)}${data.original_text.length > 50 ? '...' : ''}"`, data.timestamp);
                break;
                
            case 'tool_call_created':
                this.addActivityItem('tool-call', 'Tool Called', 
                    `${data.tool_name} - ${data.status}`, data.timestamp);
                break;
        }
        
        // Refresh analytics periodically
        this.loadAnalytics();
    }
    
    addActivityItem(type, title, description, timestamp) {
        const activityFeed = document.getElementById('activity-feed');
        const time = new Date(timestamp).toLocaleTimeString();
        
        const iconMap = {
            'session-started': 'fas fa-play',
            'session-ended': 'fas fa-stop',
            'transcription': 'fas fa-microphone',
            'tool-call': 'fas fa-tools',
            'error': 'fas fa-exclamation-triangle'
        };
        
        const activityItem = document.createElement('div');
        activityItem.className = `activity-item ${type}`;
        activityItem.innerHTML = `
            <div class="activity-icon ${type}">
                <i class="${iconMap[type] || 'fas fa-info'}"></i>
            </div>
            <div class="activity-content">
                <div class="activity-title">${title}</div>
                <div class="activity-description">${description}</div>
                <div class="activity-time">${time}</div>
            </div>
        `;
        
        // Add to top of feed
        activityFeed.insertBefore(activityItem, activityFeed.firstChild);
        
        // Limit number of items
        this.activityCount++;
        if (this.activityCount > this.maxActivityItems) {
            const items = activityFeed.querySelectorAll('.activity-item');
            if (items.length > this.maxActivityItems) {
                items[items.length - 1].remove();
            }
        }
        
        // Auto-scroll to top if enabled
        if (this.autoScroll) {
            activityFeed.scrollTop = 0;
        }
    }
    
    async loadAnalytics() {
        try {
            const response = await fetch('/api/analytics/overview?days=7');
            const data = await response.json();
            this.renderAnalytics(data);
        } catch (error) {
            console.error('Failed to load analytics:', error);
        }
    }
    
    renderAnalytics(data) {
        const container = document.getElementById('analytics-cards');
        
        container.innerHTML = `
            <div class="col-md-3 col-sm-6">
                <div class="analytics-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="card-value">${data.sessions.total}</div>
                            <div class="card-label">Total Sessions</div>
                            <div class="card-change">
                                <i class="fas fa-chart-line me-1"></i>
                                ${data.sessions.active} active
                            </div>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 col-sm-6">
                <div class="analytics-card success">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="card-value">${data.transcriptions.total}</div>
                            <div class="card-label">Transcriptions</div>
                            <div class="card-change">
                                <i class="fas fa-clock me-1"></i>
                                ${data.transcriptions.avg_response_time}s avg
                            </div>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-microphone"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 col-sm-6">
                <div class="analytics-card warning">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="card-value">${data.tool_calls.total}</div>
                            <div class="card-label">Tool Calls</div>
                            <div class="card-change">
                                <i class="fas fa-check me-1"></i>
                                ${data.tool_calls.success_rate}% success
                            </div>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 col-sm-6">
                <div class="analytics-card info">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="card-value">${data.languages.length}</div>
                            <div class="card-label">Languages</div>
                            <div class="card-change">
                                <i class="fas fa-globe me-1"></i>
                                ${data.languages.map(l => l.language).join(', ')}
                            </div>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-language"></i>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    async loadSessions() {
        try {
            const filter = document.getElementById('session-filter').value;
            const url = filter ? `/api/sessions?status=${filter}&limit=20` : '/api/sessions?limit=20';
            
            const response = await fetch(url);
            const data = await response.json();
            this.renderSessions(data.sessions);
        } catch (error) {
            console.error('Failed to load sessions:', error);
        }
    }
    
    renderSessions(sessions) {
        const tbody = document.getElementById('sessions-table-body');
        
        if (sessions.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i><br>
                        No sessions found
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = sessions.map(session => {
            const startTime = new Date(session.created_at);
            const endTime = session.ended_at ? new Date(session.ended_at) : null;
            const duration = endTime ? 
                Math.round((endTime - startTime) / 1000) + 's' : 
                'Ongoing';
            
            const statusClass = session.status === 'active' ? 'status-active' : 'status-ended';
            
            return `
                <tr>
                    <td>
                        <code>${session.id.substring(0, 8)}...</code>
                    </td>
                    <td>${startTime.toLocaleString()}</td>
                    <td>${duration}</td>
                    <td>
                        ${session.language_detected ? 
                            `<span class="language-badge">${session.language_detected}</span>` : 
                            '<span class="text-muted">-</span>'
                        }
                    </td>
                    <td>
                        <span class="badge bg-info">${session.transcription_count}</span>
                    </td>
                    <td>
                        <span class="badge bg-primary">${session.tool_call_count}</span>
                    </td>
                    <td>
                        <span class="status-badge ${statusClass}">${session.status}</span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="dashboard.viewSessionDetails('${session.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }
    
    async viewSessionDetails(sessionId) {
        try {
            const response = await fetch(`/api/sessions/${sessionId}`);
            const data = await response.json();
            this.renderSessionDetails(data);
            
            const modal = new bootstrap.Modal(document.getElementById('sessionModal'));
            modal.show();
        } catch (error) {
            console.error('Failed to load session details:', error);
        }
    }
    
    renderSessionDetails(data) {
        const container = document.getElementById('session-details-content');
        const session = data.session;
        
        // Session info
        const startTime = new Date(session.created_at);
        const endTime = session.ended_at ? new Date(session.ended_at) : null;
        const duration = endTime ? 
            Math.round((endTime - startTime) / 1000) + ' seconds' : 
            'Ongoing';
        
        let content = `
            <div class="session-info-grid">
                <div class="session-info-item">
                    <div class="session-info-label">Session ID</div>
                    <div class="session-info-value"><code>${session.id}</code></div>
                </div>
                <div class="session-info-item">
                    <div class="session-info-label">Duration</div>
                    <div class="session-info-value">${duration}</div>
                </div>
                <div class="session-info-item">
                    <div class="session-info-label">Language</div>
                    <div class="session-info-value">${session.language_detected || 'Not detected'}</div>
                </div>
                <div class="session-info-item">
                    <div class="session-info-label">Status</div>
                    <div class="session-info-value">
                        <span class="status-badge ${session.status === 'active' ? 'status-active' : 'status-ended'}">
                            ${session.status}
                        </span>
                    </div>
                </div>
            </div>
        `;
        
        // Conversation timeline
        const allItems = [];
        
        // Add transcriptions
        data.transcriptions.forEach(t => {
            allItems.push({
                type: 'transcription',
                timestamp: new Date(t.timestamp),
                data: t
            });
        });
        
        // Add tool calls
        data.tool_calls.forEach(tc => {
            allItems.push({
                type: 'tool_call',
                timestamp: new Date(tc.timestamp),
                data: tc
            });
        });
        
        // Sort by timestamp
        allItems.sort((a, b) => a.timestamp - b.timestamp);
        
        if (allItems.length > 0) {
            content += `
                <h6><i class="fas fa-timeline me-2"></i>Conversation Timeline</h6>
                <div class="conversation-timeline">
            `;
            
            allItems.forEach(item => {
                if (item.type === 'transcription') {
                    const t = item.data;
                    content += `
                        <div class="conversation-item user">
                            <div class="conversation-header">
                                <span class="conversation-type">
                                    <i class="fas fa-user me-1"></i>User Input
                                </span>
                                <span class="conversation-time">${item.timestamp.toLocaleTimeString()}</span>
                            </div>
                            <div class="conversation-content">
                                <strong>Original:</strong> ${t.original_text}<br>
                                ${t.transliterated_text ? `<strong>Transliterated:</strong> ${t.transliterated_text}<br>` : ''}
                                ${t.confidence_score ? `<small class="text-muted">Confidence: ${(t.confidence_score * 100).toFixed(1)}%</small>` : ''}
                            </div>
                        </div>
                    `;
                    
                    if (t.agent_response) {
                        content += `
                            <div class="conversation-item agent">
                                <div class="conversation-header">
                                    <span class="conversation-type">
                                        <i class="fas fa-robot me-1"></i>Agent Response
                                    </span>
                                    <span class="conversation-time">${t.response_time ? t.response_time + 's' : ''}</span>
                                </div>
                                <div class="conversation-content">${t.agent_response}</div>
                            </div>
                        `;
                    }
                } else if (item.type === 'tool_call') {
                    const tc = item.data;
                    const statusClass = tc.status === 'success' ? 'status-success' : 
                                       tc.status === 'error' ? 'status-error' : 'status-pending';
                    
                    content += `
                        <div class="conversation-item tool">
                            <div class="conversation-header">
                                <span class="conversation-type">
                                    <i class="fas fa-tools me-1"></i>${tc.tool_name}
                                </span>
                                <span class="conversation-time">
                                    <span class="status-badge ${statusClass}">${tc.status}</span>
                                    ${tc.execution_time ? tc.execution_time + 's' : ''}
                                </span>
                            </div>
                            <div class="conversation-content">
                                <strong>Query:</strong> ${tc.input_query}
                                ${tc.output_result ? `
                                    <div class="tool-details">
                                        <strong>Result:</strong><br>
                                        ${tc.output_result.substring(0, 200)}${tc.output_result.length > 200 ? '...' : ''}
                                    </div>
                                ` : ''}
                                ${tc.error_message ? `
                                    <div class="tool-details text-danger">
                                        <strong>Error:</strong> ${tc.error_message}
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    `;
                }
            });
            
            content += '</div>';
        } else {
            content += `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-comment-slash fa-2x mb-2"></i><br>
                    No conversation data available
                </div>
            `;
        }
        
        container.innerHTML = content;
    }
}

// Global functions
function clearActivity() {
    document.getElementById('activity-feed').innerHTML = '';
    dashboard.activityCount = 0;
}

function toggleAutoScroll() {
    dashboard.autoScroll = !dashboard.autoScroll;
    const btn = document.getElementById('auto-scroll-btn');
    
    if (dashboard.autoScroll) {
        btn.innerHTML = '<i class="fas fa-pause me-1"></i>Pause';
        btn.className = 'btn btn-sm btn-primary';
    } else {
        btn.innerHTML = '<i class="fas fa-play me-1"></i>Resume';
        btn.className = 'btn btn-sm btn-outline-primary';
    }
}

function loadSessions() {
    dashboard.loadSessions();
}

// Initialize dashboard when page loads
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new AgentDashboard();
});
