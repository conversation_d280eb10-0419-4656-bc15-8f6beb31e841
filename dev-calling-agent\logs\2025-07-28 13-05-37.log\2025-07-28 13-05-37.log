[ 2025-07-28 13:05:40,270 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 13:05:40,712 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4420 seconds.
[ 2025-07-28 13:05:44,806 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 13:05:53,290 ] 101 root - INFO - Fast RAG chain ready
[ 2025-07-28 13:05:53,292 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-07-28 13:05:53,292 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 13:05:54,050 ] 101 root - INFO - Fast RAG chain ready
[ 2025-07-28 13:05:54,050 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-07-28 13:05:54,051 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 13:05:54,051 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 13:05:54,054 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 13:05:54,059 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 13:05:54,067 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 13:05:54,068 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 13:05:54,068 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 13:05:54,662 ] 397 root - ERROR - Voice agent error: TTS.__init__() got an unexpected keyword argument 'speed'
[ 2025-07-28 13:05:54,664 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 375, in entrypoint
    tts=speechify.TTS(
        ^^^^^^^^^^^^^^
TypeError: TTS.__init__() got an unexpected keyword argument 'speed'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 398, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\langgraph-agent.py] line number [375] error message [TTS.__init__() got an unexpected keyword argument 'speed']
[ 2025-07-28 13:06:09,096 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 13:06:09,099 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 13:06:09,102 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-07-28 13:06:09,102 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 13:06:09,104 ] 278 livekit.agents - DEBUG - job exiting
