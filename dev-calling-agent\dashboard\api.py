"""
FastAPI Backend for Agent Dashboard
Provides REST APIs for data ingestion, retrieval, and dashboard functionality
"""
from fastapi import FastAP<PERSON>, Depends, HTTPException, Query, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from sqlalchemy import desc, func, and_
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from pydantic import BaseModel
import json
import asyncio
import logging

from .database import get_db, AgentSession, Transcription, ToolCall, SessionLog, init_database
from .data_collector import data_collector

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Agent Dashboard API",
    description="Professional dashboard for monitoring agent interactions, transcriptions, and tool calls",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Templates and static files
templates = Jinja2Templates(directory="dashboard/templates")
app.mount("/static", StaticFiles(directory="dashboard/static"), name="static")

# Pydantic models for API
class SessionCreate(BaseModel):
    user_id: Optional[str] = None
    session_type: str = "voice"
    metadata: Optional[Dict[str, Any]] = None

class TranscriptionCreate(BaseModel):
    original_text: str
    language_detected: Optional[str] = None
    confidence_score: Optional[float] = None
    audio_duration: Optional[float] = None
    transliterated_text: Optional[str] = None
    target_language: Optional[str] = None
    agent_response: Optional[str] = None
    response_language: Optional[str] = None
    response_time: Optional[float] = None
    processing_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

class ToolCallCreate(BaseModel):
    tool_name: str
    input_query: str
    tool_description: Optional[str] = None
    execution_time: Optional[float] = None
    status: str = "pending"
    output_result: Optional[str] = None
    result_count: Optional[int] = None
    is_relevant: Optional[bool] = None
    error_message: Optional[str] = None
    error_type: Optional[str] = None
    transcription_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class SessionLogCreate(BaseModel):
    level: str
    message: str
    component: Optional[str] = None
    execution_time: Optional[float] = None
    memory_usage: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # Remove disconnected connections
                self.active_connections.remove(connection)

manager = ConnectionManager()

# Initialize database on startup
@app.on_event("startup")
async def startup_event():
    init_database()
    data_collector.start_worker()
    logger.info("Dashboard API started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    data_collector.stop_worker()
    logger.info("Dashboard API shutdown")

# Main dashboard page
@app.get("/", response_class=HTMLResponse)
async def dashboard_home(request):
    return templates.TemplateResponse("dashboard.html", {"request": request})

# API Routes

# Session Management
@app.post("/api/sessions/start")
async def start_session(session_data: SessionCreate):
    """Start a new agent session"""
    session_id = data_collector.start_session(
        user_id=session_data.user_id,
        session_type=session_data.session_type,
        metadata=session_data.metadata
    )
    
    # Broadcast to WebSocket clients
    await manager.broadcast(json.dumps({
        "type": "session_started",
        "session_id": session_id,
        "timestamp": datetime.utcnow().isoformat()
    }))
    
    return {"session_id": session_id, "status": "started"}

@app.post("/api/sessions/{session_id}/end")
async def end_session(session_id: str):
    """End a session"""
    data_collector.end_session(session_id)
    
    # Broadcast to WebSocket clients
    await manager.broadcast(json.dumps({
        "type": "session_ended",
        "session_id": session_id,
        "timestamp": datetime.utcnow().isoformat()
    }))
    
    return {"session_id": session_id, "status": "ended"}

@app.get("/api/sessions")
async def get_sessions(
    limit: int = Query(50, le=100),
    offset: int = Query(0, ge=0),
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get list of sessions with pagination"""
    query = db.query(AgentSession).order_by(desc(AgentSession.created_at))
    
    if status:
        query = query.filter(AgentSession.status == status)
    
    total = query.count()
    sessions = query.offset(offset).limit(limit).all()
    
    return {
        "sessions": [
            {
                "id": s.id,
                "created_at": s.created_at.isoformat(),
                "ended_at": s.ended_at.isoformat() if s.ended_at else None,
                "user_id": s.user_id,
                "session_type": s.session_type,
                "language_detected": s.language_detected,
                "status": s.status,
                "transcription_count": len(s.transcriptions),
                "tool_call_count": len(s.tool_calls)
            }
            for s in sessions
        ],
        "total": total,
        "limit": limit,
        "offset": offset
    }

@app.get("/api/sessions/{session_id}")
async def get_session_details(session_id: str, db: Session = Depends(get_db)):
    """Get detailed session information"""
    session = db.query(AgentSession).filter(AgentSession.id == session_id).first()
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    # Get related data
    transcriptions = db.query(Transcription).filter(
        Transcription.session_id == session_id
    ).order_by(Transcription.timestamp).all()
    
    tool_calls = db.query(ToolCall).filter(
        ToolCall.session_id == session_id
    ).order_by(ToolCall.timestamp).all()
    
    logs = db.query(SessionLog).filter(
        SessionLog.session_id == session_id
    ).order_by(SessionLog.timestamp).all()
    
    return {
        "session": {
            "id": session.id,
            "created_at": session.created_at.isoformat(),
            "ended_at": session.ended_at.isoformat() if session.ended_at else None,
            "user_id": session.user_id,
            "session_type": session.session_type,
            "language_detected": session.language_detected,
            "language_config": session.language_config,
            "status": session.status,
            "metadata": session.metadata
        },
        "transcriptions": [
            {
                "id": t.id,
                "timestamp": t.timestamp.isoformat(),
                "original_text": t.original_text,
                "language_detected": t.language_detected,
                "confidence_score": t.confidence_score,
                "audio_duration": t.audio_duration,
                "transliterated_text": t.transliterated_text,
                "target_language": t.target_language,
                "agent_response": t.agent_response,
                "response_language": t.response_language,
                "response_time": t.response_time,
                "processing_time": t.processing_time,
                "metadata": t.metadata
            }
            for t in transcriptions
        ],
        "tool_calls": [
            {
                "id": tc.id,
                "timestamp": tc.timestamp.isoformat(),
                "tool_name": tc.tool_name,
                "tool_description": tc.tool_description,
                "input_query": tc.input_query,
                "execution_time": tc.execution_time,
                "status": tc.status,
                "output_result": tc.output_result,
                "result_count": tc.result_count,
                "is_relevant": tc.is_relevant,
                "error_message": tc.error_message,
                "error_type": tc.error_type,
                "transcription_id": tc.transcription_id,
                "metadata": tc.metadata
            }
            for tc in tool_calls
        ],
        "logs": [
            {
                "id": l.id,
                "timestamp": l.timestamp.isoformat(),
                "level": l.level,
                "message": l.message,
                "component": l.component,
                "execution_time": l.execution_time,
                "memory_usage": l.memory_usage,
                "metadata": l.metadata
            }
            for l in logs
        ]
    }

# Data ingestion endpoints
@app.post("/api/transcriptions")
async def create_transcription(transcription: TranscriptionCreate):
    """Create a new transcription record"""
    transcription_id = data_collector.log_transcription(
        original_text=transcription.original_text,
        language_detected=transcription.language_detected,
        confidence_score=transcription.confidence_score,
        audio_duration=transcription.audio_duration,
        transliterated_text=transcription.transliterated_text,
        target_language=transcription.target_language,
        agent_response=transcription.agent_response,
        response_language=transcription.response_language,
        response_time=transcription.response_time,
        processing_time=transcription.processing_time,
        metadata=transcription.metadata
    )
    
    # Broadcast to WebSocket clients
    await manager.broadcast(json.dumps({
        "type": "transcription_created",
        "transcription_id": transcription_id,
        "original_text": transcription.original_text,
        "timestamp": datetime.utcnow().isoformat()
    }))
    
    return {"transcription_id": transcription_id, "status": "created"}

@app.post("/api/tool-calls")
async def create_tool_call(tool_call: ToolCallCreate):
    """Create a new tool call record"""
    tool_call_id = data_collector.log_tool_call(
        tool_name=tool_call.tool_name,
        input_query=tool_call.input_query,
        tool_description=tool_call.tool_description,
        execution_time=tool_call.execution_time,
        status=tool_call.status,
        output_result=tool_call.output_result,
        result_count=tool_call.result_count,
        is_relevant=tool_call.is_relevant,
        error_message=tool_call.error_message,
        error_type=tool_call.error_type,
        transcription_id=tool_call.transcription_id,
        metadata=tool_call.metadata
    )
    
    # Broadcast to WebSocket clients
    await manager.broadcast(json.dumps({
        "type": "tool_call_created",
        "tool_call_id": tool_call_id,
        "tool_name": tool_call.tool_name,
        "status": tool_call.status,
        "timestamp": datetime.utcnow().isoformat()
    }))
    
    return {"tool_call_id": tool_call_id, "status": "created"}

@app.post("/api/logs")
async def create_log(log: SessionLogCreate):
    """Create a new session log entry"""
    log_id = data_collector.log_session_event(
        level=log.level,
        message=log.message,
        component=log.component,
        execution_time=log.execution_time,
        memory_usage=log.memory_usage,
        metadata=log.metadata
    )
    
    return {"log_id": log_id, "status": "created"}

# Analytics endpoints
@app.get("/api/analytics/overview")
async def get_analytics_overview(
    days: int = Query(7, ge=1, le=30),
    db: Session = Depends(get_db)
):
    """Get dashboard analytics overview"""
    start_date = datetime.utcnow() - timedelta(days=days)
    
    # Session statistics
    total_sessions = db.query(AgentSession).filter(
        AgentSession.created_at >= start_date
    ).count()
    
    active_sessions = db.query(AgentSession).filter(
        and_(AgentSession.status == "active", AgentSession.created_at >= start_date)
    ).count()
    
    # Transcription statistics
    total_transcriptions = db.query(Transcription).filter(
        Transcription.timestamp >= start_date
    ).count()
    
    avg_response_time = db.query(func.avg(Transcription.response_time)).filter(
        and_(Transcription.timestamp >= start_date, Transcription.response_time.isnot(None))
    ).scalar() or 0
    
    # Tool call statistics
    total_tool_calls = db.query(ToolCall).filter(
        ToolCall.timestamp >= start_date
    ).count()
    
    successful_tool_calls = db.query(ToolCall).filter(
        and_(ToolCall.timestamp >= start_date, ToolCall.status == "success")
    ).count()
    
    # Language distribution
    language_stats = db.query(
        AgentSession.language_detected,
        func.count(AgentSession.id).label('count')
    ).filter(
        AgentSession.created_at >= start_date
    ).group_by(AgentSession.language_detected).all()
    
    return {
        "period_days": days,
        "sessions": {
            "total": total_sessions,
            "active": active_sessions,
            "completed": total_sessions - active_sessions
        },
        "transcriptions": {
            "total": total_transcriptions,
            "avg_response_time": round(avg_response_time, 2) if avg_response_time else 0
        },
        "tool_calls": {
            "total": total_tool_calls,
            "successful": successful_tool_calls,
            "success_rate": round((successful_tool_calls / total_tool_calls * 100), 2) if total_tool_calls > 0 else 0
        },
        "languages": [
            {"language": lang or "unknown", "count": count}
            for lang, count in language_stats
        ]
    }

# WebSocket endpoint for real-time updates
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # Echo back for now - can be extended for client commands
            await manager.send_personal_message(f"Echo: {data}", websocket)
    except WebSocketDisconnect:
        manager.disconnect(websocket)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
