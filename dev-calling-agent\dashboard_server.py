#!/usr/bin/env python3
"""
Agent Dashboard Server
Professional dashboard for monitoring agent interactions, transcriptions, and tool calls
"""
import os
import sys
import uvicorn
import argparse
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dashboard.api import app
from dashboard.database import init_database
from dashboard.data_collector import data_collector

def main():
    """Main entry point for the dashboard server"""
    parser = argparse.ArgumentParser(description="Agent Dashboard Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development")
    parser.add_argument("--log-level", default="info", choices=["debug", "info", "warning", "error"], 
                       help="Log level")
    
    args = parser.parse_args()
    
    print("🚀 Starting Agent Dashboard Server...")
    print(f"📊 Dashboard will be available at: http://{args.host}:{args.port}")
    print(f"🔧 API documentation at: http://{args.host}:{args.port}/docs")
    
    # Initialize database
    try:
        init_database()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize database: {e}")
        sys.exit(1)
    
    # Start data collector
    try:
        data_collector.start_worker()
        print("✅ Data collector started")
    except Exception as e:
        print(f"❌ Failed to start data collector: {e}")
        sys.exit(1)
    
    # Run the server
    try:
        uvicorn.run(
            "dashboard.api:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level=args.log_level,
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Shutting down dashboard server...")
    except Exception as e:
        print(f"❌ Server error: {e}")
        sys.exit(1)
    finally:
        # Clean shutdown
        try:
            data_collector.stop_worker()
            print("✅ Data collector stopped")
        except Exception as e:
            print(f"⚠️ Error stopping data collector: {e}")

if __name__ == "__main__":
    main()
