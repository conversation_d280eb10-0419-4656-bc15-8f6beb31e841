[ 2025-07-28 13:15:56,247 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 13:15:56,660 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4137 seconds.
[ 2025-07-28 13:16:00,502 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 13:16:09,135 ] 101 root - INFO - Fast RAG chain ready
[ 2025-07-28 13:16:09,136 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-07-28 13:16:09,137 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 13:16:10,035 ] 101 root - INFO - Fast RAG chain ready
[ 2025-07-28 13:16:10,035 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-07-28 13:16:10,040 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 13:16:10,040 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 13:16:10,048 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 13:16:10,052 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 13:16:10,056 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 13:16:10,058 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 13:16:10,058 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 13:16:11,060 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 13:16:11,060 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 13:16:12,215 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 13:16:21,527 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 13:16:41,381 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 13:16:42,037 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 13:16:42,246 ] 180 root - INFO - \u26a1 Langdetect: so
[ 2025-07-28 13:16:42,248 ] 259 root - WARNING - Unsupported language code: so. Defaulting to English.
[ 2025-07-28 13:16:42,248 ] 207 root - INFO - \u26a1 Language detection: 0.211s
[ 2025-07-28 13:16:42,249 ] 235 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-07-28 13:16:42,249 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 13:16:42,249 ] 45 root - INFO - \U0001f310 Fast web search: 'Kolkata weather today...'
[ 2025-07-28 13:16:44,920 ] 59 root - INFO - \u26a1 Web search: 2.67s, 2 results
[ 2025-07-28 13:16:44,920 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.6714 seconds.
[ 2025-07-28 13:16:44,921 ] 247 root - INFO - \U0001f310 Web search: 2.88s, 2 results
[ 2025-07-28 13:16:44,923 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 13:17:07,756 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 13:17:09,086 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 13:17:26,966 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 13:17:26,967 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 13:17:26,969 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 13:17:26,970 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 13:17:26,970 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
