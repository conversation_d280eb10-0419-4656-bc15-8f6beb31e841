"""
Database models and configuration for the Agent Dashboard
"""
import os
import uuid
from datetime import datetime
from typing import Optional, List
from sqlalchemy import create_engine, Column, String, DateTime, Text, Integer, Float, Boolean, Foreign<PERSON>ey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship, Session
from sqlalchemy.dialects.sqlite import uuid4
import json

# Database configuration
DATABASE_URL = "sqlite:///./dashboard.db"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class AgentSession(Base):
    """Agent conversation session"""
    __tablename__ = "agent_sessions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    created_at = Column(DateTime, default=datetime.utcnow)
    ended_at = Column(DateTime, nullable=True)
    user_id = Column(String, nullable=True)
    session_type = Column(String, default="voice")  # voice, text, etc.
    language_detected = Column(String, nullable=True)
    language_config = Column(JSON, nullable=True)
    status = Column(String, default="active")  # active, ended, error
    metadata = Column(JSON, nullable=True)
    
    # Relationships
    transcriptions = relationship("Transcription", back_populates="session", cascade="all, delete-orphan")
    tool_calls = relationship("ToolCall", back_populates="session", cascade="all, delete-orphan")
    logs = relationship("SessionLog", back_populates="session", cascade="all, delete-orphan")

class Transcription(Base):
    """Audio transcription and text processing"""
    __tablename__ = "transcriptions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String, ForeignKey("agent_sessions.id"), nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # Audio and transcription data
    audio_duration = Column(Float, nullable=True)
    original_text = Column(Text, nullable=False)
    language_detected = Column(String, nullable=True)
    confidence_score = Column(Float, nullable=True)
    
    # Transliteration data
    transliterated_text = Column(Text, nullable=True)
    target_language = Column(String, nullable=True)
    
    # Agent response
    agent_response = Column(Text, nullable=True)
    response_language = Column(String, nullable=True)
    response_time = Column(Float, nullable=True)  # Response time in seconds
    
    # Processing metadata
    processing_time = Column(Float, nullable=True)
    metadata = Column(JSON, nullable=True)
    
    # Relationships
    session = relationship("AgentSession", back_populates="transcriptions")

class ToolCall(Base):
    """Tool execution tracking"""
    __tablename__ = "tool_calls"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String, ForeignKey("agent_sessions.id"), nullable=False)
    transcription_id = Column(String, ForeignKey("transcriptions.id"), nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # Tool information
    tool_name = Column(String, nullable=False)
    tool_description = Column(Text, nullable=True)
    input_query = Column(Text, nullable=False)
    
    # Execution details
    execution_time = Column(Float, nullable=True)  # Execution time in seconds
    status = Column(String, default="pending")  # pending, success, error, timeout
    
    # Results
    output_result = Column(Text, nullable=True)
    result_count = Column(Integer, nullable=True)
    is_relevant = Column(Boolean, nullable=True)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    error_type = Column(String, nullable=True)
    
    # Metadata
    metadata = Column(JSON, nullable=True)
    
    # Relationships
    session = relationship("AgentSession", back_populates="tool_calls")
    transcription = relationship("Transcription")

class SessionLog(Base):
    """Detailed session logs"""
    __tablename__ = "session_logs"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String, ForeignKey("agent_sessions.id"), nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # Log details
    level = Column(String, nullable=False)  # INFO, DEBUG, WARNING, ERROR
    message = Column(Text, nullable=False)
    component = Column(String, nullable=True)  # agent, tool, transcription, etc.
    
    # Performance metrics
    execution_time = Column(Float, nullable=True)
    memory_usage = Column(Float, nullable=True)
    
    # Additional data
    metadata = Column(JSON, nullable=True)
    
    # Relationships
    session = relationship("AgentSession", back_populates="logs")

# Create all tables
def create_tables():
    """Create all database tables"""
    Base.metadata.create_all(bind=engine)

def init_database():
    """Initialize the database"""
    create_tables()
    print("✅ Database initialized successfully")

if __name__ == "__main__":
    init_database()
