#!/usr/bin/env python3
"""
Dashboard Test Script
Simulates agent interactions to test dashboard functionality
"""
import asyncio
import time
import random
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dashboard.database import init_database
from dashboard.data_collector import data_collector

# Sample data for testing
SAMPLE_QUERIES = [
    "What is machine learning?",
    "How does neural network work?",
    "Explain quantum computing",
    "What are the latest AI developments?",
    "How to implement REST API?",
    "What is Docker containerization?",
    "Explain microservices architecture",
    "How does blockchain work?",
    "What is cloud computing?",
    "Explain DevOps practices"
]

SAMPLE_LANGUAGES = [
    ("en", "English", {"name": "English", "sample_phrases": {"greeting": "Hello"}}),
    ("hi", "Hindi", {"name": "Hindi", "sample_phrases": {"greeting": "Namaste"}}),
    ("ta", "Tamil", {"name": "Tamil", "sample_phrases": {"greeting": "Vanakkam"}}),
    ("te", "Telugu", {"name": "Telugu", "sample_phrases": {"greeting": "Namaskaram"}}),
    ("de", "German", {"name": "German", "sample_phrases": {"greeting": "Hallo"}}),
    ("fr", "French", {"name": "French", "sample_phrases": {"greeting": "Bonjour"}})
]

SAMPLE_RESPONSES = [
    "Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed.",
    "Neural networks are computing systems inspired by biological neural networks. They consist of interconnected nodes that process information.",
    "Quantum computing uses quantum mechanical phenomena to perform calculations that would be impossible for classical computers.",
    "Recent AI developments include large language models, computer vision advances, and autonomous systems.",
    "REST APIs use HTTP methods to enable communication between different software applications in a standardized way."
]

TOOL_NAMES = ["vector_database_search", "web_search"]

async def simulate_session():
    """Simulate a complete agent session"""
    print("🎭 Starting session simulation...")
    
    # Start session
    session_id = data_collector.start_session(
        user_id=f"test_user_{random.randint(1000, 9999)}",
        session_type="test_simulation",
        metadata={"simulation": True, "test_run": True}
    )
    
    print(f"📝 Session started: {session_id}")
    
    # Select random language
    lang_code, lang_name, lang_config = random.choice(SAMPLE_LANGUAGES)
    data_collector.update_session_language(lang_code, lang_config)
    print(f"🌍 Language set: {lang_name}")
    
    # Simulate conversation
    num_interactions = random.randint(3, 8)
    
    for i in range(num_interactions):
        print(f"💬 Interaction {i+1}/{num_interactions}")
        
        # User input
        query = random.choice(SAMPLE_QUERIES)
        
        # Log transcription
        transcription_start = time.time()
        await asyncio.sleep(random.uniform(0.1, 0.5))  # Simulate processing time
        
        transcription_id = data_collector.log_transcription(
            original_text=query,
            language_detected=lang_code,
            confidence_score=random.uniform(0.85, 0.99),
            processing_time=time.time() - transcription_start
        )
        
        # Simulate tool call
        if random.choice([True, False]):  # 50% chance of tool call
            tool_name = random.choice(TOOL_NAMES)
            tool_start = time.time()
            
            # Log tool call start
            tool_call_id = data_collector.log_tool_call(
                tool_name=tool_name,
                input_query=query,
                tool_description=f"Simulated {tool_name} execution",
                status="pending"
            )
            
            # Simulate tool execution
            await asyncio.sleep(random.uniform(0.5, 2.0))
            execution_time = time.time() - tool_start
            
            # Random success/failure
            if random.random() > 0.1:  # 90% success rate
                result = random.choice(SAMPLE_RESPONSES)
                data_collector.log_tool_call(
                    tool_name=tool_name,
                    input_query=query,
                    execution_time=execution_time,
                    status="success",
                    output_result=result,
                    is_relevant=True,
                    result_count=random.randint(1, 5),
                    metadata={"simulation": True}
                )
                print(f"  ✅ {tool_name} succeeded in {execution_time:.2f}s")
            else:
                data_collector.log_tool_call(
                    tool_name=tool_name,
                    input_query=query,
                    execution_time=execution_time,
                    status="error",
                    error_message="Simulated error for testing",
                    error_type="SimulationError"
                )
                print(f"  ❌ {tool_name} failed in {execution_time:.2f}s")
        
        # Agent response
        response = random.choice(SAMPLE_RESPONSES)
        response_time = random.uniform(0.5, 2.0)
        
        # Update transcription with response
        data_collector.log_transcription(
            original_text=query,
            language_detected=lang_code,
            agent_response=response,
            response_language=lang_code,
            response_time=response_time,
            confidence_score=random.uniform(0.85, 0.99),
            processing_time=response_time
        )
        
        print(f"  🤖 Agent responded in {response_time:.2f}s")
        
        # Random delay between interactions
        await asyncio.sleep(random.uniform(1.0, 3.0))
    
    # Log session events
    data_collector.log_session_event(
        level="INFO",
        message=f"Session completed with {num_interactions} interactions",
        component="test_simulator",
        metadata={"interactions": num_interactions, "language": lang_name}
    )
    
    # End session
    data_collector.end_session(session_id)
    print(f"🏁 Session ended: {session_id}")
    
    return session_id

async def run_simulation(num_sessions=3, concurrent=False):
    """Run multiple session simulations"""
    print(f"🚀 Starting dashboard simulation with {num_sessions} sessions")
    print(f"🔄 Concurrent mode: {'ON' if concurrent else 'OFF'}")
    
    # Initialize database
    init_database()
    print("✅ Database initialized")
    
    # Start data collector
    data_collector.start_worker()
    print("✅ Data collector started")
    
    try:
        if concurrent:
            # Run sessions concurrently
            tasks = [simulate_session() for _ in range(num_sessions)]
            session_ids = await asyncio.gather(*tasks)
        else:
            # Run sessions sequentially
            session_ids = []
            for i in range(num_sessions):
                print(f"\n--- Session {i+1}/{num_sessions} ---")
                session_id = await simulate_session()
                session_ids.append(session_id)
                
                # Delay between sessions
                if i < num_sessions - 1:
                    await asyncio.sleep(random.uniform(2.0, 5.0))
        
        print(f"\n🎉 Simulation completed!")
        print(f"📊 Generated {len(session_ids)} sessions")
        print(f"🌐 Dashboard available at: http://localhost:8000")
        print(f"📈 API docs at: http://localhost:8000/docs")
        
        return session_ids
        
    except KeyboardInterrupt:
        print("\n🛑 Simulation interrupted by user")
    except Exception as e:
        print(f"❌ Simulation error: {e}")
    finally:
        # Stop data collector
        data_collector.stop_worker()
        print("✅ Data collector stopped")

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Dashboard Test Simulation")
    parser.add_argument("--sessions", type=int, default=3, help="Number of sessions to simulate")
    parser.add_argument("--concurrent", action="store_true", help="Run sessions concurrently")
    
    args = parser.parse_args()
    
    # Run simulation
    asyncio.run(run_simulation(args.sessions, args.concurrent))

if __name__ == "__main__":
    main()
