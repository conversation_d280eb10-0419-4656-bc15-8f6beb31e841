/* Agent Dashboard Professional Styling */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 8px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.15s ease-in-out;
}

body {
    background-color: #f5f6fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* Analytics Cards */
.analytics-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.analytics-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.analytics-card.success {
    background: linear-gradient(135deg, var(--success-color), #146c43);
}

.analytics-card.warning {
    background: linear-gradient(135deg, var(--warning-color), #e6a700);
}

.analytics-card.info {
    background: linear-gradient(135deg, var(--info-color), #0aa2c0);
}

.analytics-card .card-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.analytics-card .card-value {
    font-size: 2rem;
    font-weight: 700;
    margin: 0.5rem 0;
}

.analytics-card .card-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.analytics-card .card-change {
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

/* Activity Feed */
.activity-feed {
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
    background-color: #f8f9fa;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: white;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    transition: var(--transition);
}

.activity-item:hover {
    box-shadow: var(--box-shadow);
}

.activity-item.session-started {
    border-left-color: var(--success-color);
}

.activity-item.session-ended {
    border-left-color: var(--warning-color);
}

.activity-item.transcription {
    border-left-color: var(--info-color);
}

.activity-item.tool-call {
    border-left-color: var(--primary-color);
}

.activity-item.error {
    border-left-color: var(--danger-color);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.1rem;
    color: white;
}

.activity-icon.session-started {
    background-color: var(--success-color);
}

.activity-icon.session-ended {
    background-color: var(--warning-color);
}

.activity-icon.transcription {
    background-color: var(--info-color);
}

.activity-icon.tool-call {
    background-color: var(--primary-color);
}

.activity-icon.error {
    background-color: var(--danger-color);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--dark-color);
}

.activity-description {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.activity-time {
    color: var(--secondary-color);
    font-size: 0.8rem;
}

/* Tables */
.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
    background-color: var(--light-color);
}

.table td {
    vertical-align: middle;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-active {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-ended {
    background-color: #f8d7da;
    color: #842029;
}

.status-pending {
    background-color: #fff3cd;
    color: #664d03;
}

.status-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-error {
    background-color: #f8d7da;
    color: #842029;
}

/* Language Badges */
.language-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: var(--primary-color);
    color: white;
}

/* Session Details */
.session-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.session-info-item {
    background-color: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
}

.session-info-label {
    font-size: 0.8rem;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.session-info-value {
    font-weight: 600;
    color: var(--dark-color);
}

/* Transcription and Tool Call Items */
.conversation-item {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--primary-color);
}

.conversation-item.user {
    border-left-color: var(--info-color);
    background-color: #f0f9ff;
}

.conversation-item.agent {
    border-left-color: var(--success-color);
    background-color: #f0fdf4;
}

.conversation-item.tool {
    border-left-color: var(--warning-color);
    background-color: #fffbeb;
}

.conversation-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.conversation-type {
    font-weight: 600;
    color: var(--dark-color);
}

.conversation-time {
    color: var(--secondary-color);
    font-size: 0.8rem;
}

.conversation-content {
    color: var(--dark-color);
    line-height: 1.5;
}

.tool-details {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 0.75rem;
    border-radius: 4px;
    margin-top: 0.5rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

/* Metrics */
.metric-item {
    text-align: center;
    padding: 1rem;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.metric-label {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-top: 0.25rem;
}

/* Connection Status */
#connection-status {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.connection-disconnected #connection-status {
    color: var(--danger-color) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .analytics-card {
        margin-bottom: 1rem;
    }
    
    .session-info-grid {
        grid-template-columns: 1fr;
    }
    
    .activity-feed {
        max-height: 300px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scrollbar Styling */
.activity-feed::-webkit-scrollbar {
    width: 6px;
}

.activity-feed::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.activity-feed::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 3px;
}

.activity-feed::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}
